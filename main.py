"""
Main application entry point for BrainSpace session management.
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

from config import config
from session_manager import session_manager
from session_service import session_service
from telegram_bot_handler import bot_handler

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./log/brainspace_session.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class BrainSpaceApp:
    """Main application class."""
    
    def __init__(self):
        self.running = False
    
    async def start(self):
        """Start the application."""
        logger.info("Starting BrainSpace Session Manager...")
        
        # Validate configuration
        config_errors = config.validate_config()
        if config_errors:
            logger.error("Configuration errors:")
            for error in config_errors:
                logger.error(f"  - {error}")
            sys.exit(1)
        
        try:
            # Start session monitoring service
            session_service.start()
            
            # Start Telegram bot
            await bot_handler.start_bot()
            
            self.running = True
            logger.info("BrainSpace Session Manager started successfully")
            
            # Send startup notification
            await self._send_startup_notification()
            
            # Keep the application running
            await self._run_forever()
            
        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            await self.stop()
            sys.exit(1)
    
    async def stop(self):
        """Stop the application."""
        if not self.running:
            return
        
        logger.info("Stopping BrainSpace Session Manager...")
        self.running = False
        
        try:
            # Stop services
            session_service.stop()
            await bot_handler.stop_bot()
            
            logger.info("BrainSpace Session Manager stopped")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def _run_forever(self):
        """Keep the application running."""
        try:
            while self.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
    
    async def _send_startup_notification(self):
        """Send startup notification."""
        try:
            status = session_manager.get_session_status()
            
            if status['authenticated']:
                message = (
                    "🚀 *BrainSpace Started*\n\n"
                    "✅ Session is active\n"
                    f"⏰ Expires: {status['expires_at']}\n"
                    f"🕐 Time remaining: {status['time_remaining']}"
                )
            else:
                message = (
                    "🚀 *BrainSpace Started*\n\n"
                    "❌ No active session\n"
                    "Use `/login` to authenticate"
                )
            
            await session_service.notify_custom(message)
            
        except Exception as e:
            logger.error(f"Failed to send startup notification: {e}")


# Global app instance
app = BrainSpaceApp()


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}")
    asyncio.create_task(app.stop())


async def main():
    """Main entry point."""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await app.start()
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
    finally:
        await app.stop()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application terminated by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
