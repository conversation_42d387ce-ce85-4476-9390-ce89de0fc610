"""
Debug script to test session and authentication.
"""

import json
from session_manager import session_manager
from brain_api_client import BrainAPIClient


def debug_session():
    """Debug current session state."""
    print("🔍 Session Debug Information")
    print("=" * 40)
    
    # Check session manager
    print("\n📋 Session Manager Status:")
    is_auth = session_manager.is_authenticated()
    print(f"   Authenticated: {is_auth}")
    
    session = session_manager.get_session()
    if session:
        print(f"   Token length: {len(session.token)}")
        print(f"   Token preview: {session.token[:100]}...")
        print(f"   Expires: {session.expiry}")
        print(f"   Minutes left: {session.minutes_until_expiry}")
        print(f"   Is expired: {session.is_expired}")
        
        # Try to parse token as JSON (cookies)
        try:
            cookies = json.loads(session.token)
            print(f"   Token is cookies: {len(cookies)} cookies")
            for name, value in cookies.items():
                print(f"     {name}: {value[:20]}...")
        except:
            print("   Token is not JSON (might be JWT)")
    else:
        print("   No session found")
    
    return session


def test_authentication():
    """Test authentication endpoint."""
    print("\n🔐 Testing Authentication Endpoint:")
    
    session = debug_session()
    if not session:
        print("❌ No session to test")
        return
    
    client = BrainAPIClient()
    
    # Test token validity
    print("\n🧪 Testing token validity:")
    is_valid = client.test_token(session.token)
    print(f"   Token valid: {is_valid}")
    
    # Test authenticated session
    print("\n🧪 Testing authenticated session:")
    try:
        auth_session = client.get_authenticated_session(session.token)
        response = auth_session.get("https://api.worldquantbrain.com/authentication")
        print(f"   GET /authentication: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        # Check cookies in the session
        print(f"   Session cookies: {len(auth_session.cookies)} cookies")
        for cookie in auth_session.cookies:
            print(f"     {cookie.name}: {cookie.value[:20]}...")
            
    except Exception as e:
        print(f"   Error: {e}")


def test_simple_request():
    """Test a simple API request."""
    print("\n📡 Testing Simple API Request:")
    
    session = debug_session()
    if not session:
        print("❌ No session to test")
        return
    
    client = BrainAPIClient()
    
    try:
        # Try to get simulation options
        response = client.make_authenticated_request(
            'OPTIONS', '/simulations', session.token
        )
        print(f"   OPTIONS /simulations: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
    except Exception as e:
        print(f"   Error: {e}")


def test_manual_request():
    """Test manual request with session cookies."""
    print("\n🔧 Testing Manual Request:")
    
    session = debug_session()
    if not session:
        print("❌ No session to test")
        return
    
    try:
        import requests
        
        # Parse cookies from token
        cookies = json.loads(session.token)
        
        # Create manual session
        manual_session = requests.Session()
        for name, value in cookies.items():
            manual_session.cookies.set(name, value)
        
        # Test request
        response = manual_session.get("https://api.worldquantbrain.com/authentication")
        print(f"   Manual GET /authentication: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        # Try simulations endpoint
        response = manual_session.options("https://api.worldquantbrain.com/simulations")
        print(f"   Manual OPTIONS /simulations: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
    except Exception as e:
        print(f"   Error: {e}")


def main():
    """Run all debug tests."""
    print("🧪 BrainSpace Session Debug Suite")
    print("=" * 50)
    
    debug_session()
    test_authentication()
    test_simple_request()
    test_manual_request()
    
    print("\n" + "=" * 50)
    print("Debug complete!")


if __name__ == "__main__":
    main()
