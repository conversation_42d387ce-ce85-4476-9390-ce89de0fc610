"""
Fix existing session token format.
"""

import json
import os
from config import config


def fix_session_token():
    """Fix the session token format from dict string to proper JSON."""
    session_file = config.SESSION_FILE_PATH
    
    if not os.path.exists(session_file):
        print("❌ No session file found")
        return
    
    try:
        # Read current session
        with open(session_file, 'r') as f:
            session_data = json.load(f)
        
        print("🔍 Current session data:")
        print(f"   Token: {session_data['token'][:100]}...")
        
        # Check if token is in wrong format (dict string)
        token = session_data['token']
        
        if token.startswith("{'") and token.endswith("'}"):
            print("🔧 Fixing token format...")
            
            # Convert dict string to proper dict
            import ast
            token_dict = ast.literal_eval(token)
            
            # Convert to proper JSON
            fixed_token = json.dumps(token_dict)
            
            print(f"✅ Fixed token: {fixed_token}")
            
            # Update session data
            session_data['token'] = fixed_token
            
            # Save fixed session
            with open(session_file, 'w') as f:
                json.dump(session_data, f, indent=2)
            
            print("✅ Session file updated successfully")
            
        else:
            print("✅ Token format is already correct")
            
    except Exception as e:
        print(f"❌ Error fixing session: {e}")


def test_fixed_session():
    """Test the fixed session."""
    from session_manager import session_manager
    from brain_api_client import BrainAPIClient
    
    print("\n🧪 Testing fixed session:")
    
    # Reload session
    session_manager._load_session()
    
    session = session_manager.get_session()
    if not session:
        print("❌ No session found")
        return
    
    print(f"   Token preview: {session.token[:50]}...")
    
    # Test if it's valid JSON
    try:
        cookies = json.loads(session.token)
        print(f"   ✅ Token is valid JSON with {len(cookies)} cookies")
        
        # Test authentication
        client = BrainAPIClient()
        is_valid = client.test_token(session.token)
        print(f"   Token validity: {is_valid}")
        
        # Test a simple request
        try:
            response = client.make_authenticated_request(
                'OPTIONS', '/simulations', session.token
            )
            print(f"   OPTIONS /simulations: {response.status_code}")
            
        except Exception as e:
            print(f"   Request error: {e}")
            
    except json.JSONDecodeError:
        print("   ❌ Token is still not valid JSON")


if __name__ == "__main__":
    print("🔧 Session Token Fix Utility")
    print("=" * 30)
    
    fix_session_token()
    test_fixed_session()
    
    print("\n" + "=" * 30)
    print("Fix complete! Try running brain_api_examples.py again.")
