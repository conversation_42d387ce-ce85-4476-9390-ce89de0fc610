"""
Brain API client for authentication and session management.
"""

import requests
from datetime import datetime, timedelta
from urllib.parse import urljoin
from typing import Optional, Tuple
import logging

from config import config
from session_models import AuthResponse

logger = logging.getLogger(__name__)


class BrainAPIClient:
    """Client for Brain API authentication."""

    def __init__(self):
        self.base_url = config.BRAIN_API_BASE_URL
        self.session = requests.Session()
        self.session.timeout = config.REQUEST_TIMEOUT

    def authenticate(self, username: str, password: str) -> AuthResponse:
        """
        Authenticate with Brain API.

        Returns:
            AuthResponse with authentication result
        """
        try:
            # Set basic auth
            self.session.auth = (username, password)

            # Attempt authentication
            response = self.session.post(f"{self.base_url}/authentication")

            if response.status_code == 201:
                # Successful authentication
                data = response.json()
                expiry_seconds = data.get("token", {}).get("expiry", 3600)
                expiry = datetime.now() + timedelta(seconds=expiry_seconds)

                # Extract token from session cookies or headers
                token = self._extract_token(response)

                return AuthResponse(
                    success=True,
                    token=token,
                    expiry=expiry
                )

            elif response.status_code == 401:
                # Check if biometric auth is required
                www_auth = response.headers.get("WWW-Authenticate", "")
                if www_auth == "persona":
                    location = response.headers.get("Location", "")
                    biometric_url = urljoin(response.url, location)

                    return AuthResponse(
                        success=False,
                        requires_biometric=True,
                        biometric_url=biometric_url
                    )
                else:
                    return AuthResponse(
                        success=False,
                        error="Invalid credentials"
                    )

            else:
                return AuthResponse(
                    success=False,
                    error=f"Authentication failed: {response.status_code}"
                )

        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return AuthResponse(
                success=False,
                error=str(e)
            )

    def complete_biometric_auth(self, biometric_url: str) -> AuthResponse:
        """
        Complete biometric authentication after user confirms.

        Args:
            biometric_url: The persona URL to complete authentication

        Returns:
            AuthResponse with final authentication result
        """
        try:
            response = self.session.post(biometric_url)

            if response.status_code == 201:
                # Successful authentication
                data = response.json()
                expiry_seconds = data.get("token", {}).get("expiry", 3600)
                expiry = datetime.now() + timedelta(seconds=expiry_seconds)

                # Extract token from session
                token = self._extract_token(response)

                return AuthResponse(
                    success=True,
                    token=token,
                    expiry=expiry
                )
            else:
                return AuthResponse(
                    success=False,
                    error=f"Biometric authentication failed: {response.status_code}"
                )

        except Exception as e:
            logger.error(f"Biometric authentication error: {e}")
            return AuthResponse(
                success=False,
                error=str(e)
            )

    def _extract_token(self, response: requests.Response) -> str:
        """Extract authentication token/session from response."""
        # For Brain API, the authentication is handled via session cookies
        # We'll serialize the cookies as our "token"
        cookies_dict = dict(self.session.cookies)

        if cookies_dict:
            # Return properly serialized cookies as JSON
            import json
            return json.dumps(cookies_dict)

        # Fallback: try to get JWT from response
        try:
            data = response.json()
            if 'token' in data:
                return data['token']
        except:
            pass

        # Last resort: return empty dict as JSON
        import json
        return json.dumps({})

    def test_token(self, token: str) -> bool:
        """Test if a token is still valid."""
        try:
            # Create test session with the token
            test_session = self.get_authenticated_session(token)

            # Test with a simple GET request
            response = test_session.get(f"{self.base_url}/authentication")
            return response.status_code == 200

        except Exception as e:
            logger.error(f"Token test error: {e}")
            return False

    def get_authenticated_session(self, token: str) -> requests.Session:
        """Get a session with authentication cookies set."""
        session = requests.Session()
        session.timeout = config.REQUEST_TIMEOUT

        try:
            # Token is actually serialized cookies for Brain API
            import json
            cookies_dict = json.loads(token)

            # Set cookies in the session
            for name, value in cookies_dict.items():
                session.cookies.set(name, value)

        except (json.JSONDecodeError, TypeError):
            # Fallback: treat as Bearer token
            session.headers['Authorization'] = f'Bearer {token}'

        return session

    def make_authenticated_request(self, method: str, endpoint: str, token: str, **kwargs) -> requests.Response:
        """
        Make an authenticated request to the Brain API.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (e.g., '/simulations', '/alphas/123')
            token: Authentication token
            **kwargs: Additional arguments for requests (json, params, etc.)

        Returns:
            requests.Response object

        Example:
            # Submit a simulation
            response = client.make_authenticated_request(
                'POST', '/simulations', token,
                json={'type': 'REGULAR', 'regular': 'close', 'settings': {...}}
            )

            # Get alpha details
            response = client.make_authenticated_request(
                'GET', '/alphas/alpha_123', token
            )
        """
        url = f"{self.base_url}{endpoint}"
        session = self.get_authenticated_session(token)

        try:
            response = session.request(method.upper(), url, **kwargs)
            logger.info(
                f"{method.upper()} {endpoint} - Status: {response.status_code}")
            return response

        except Exception as e:
            logger.error(f"Request failed {method.upper()} {endpoint}: {e}")
            raise
