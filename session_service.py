"""
Session service for monitoring and notifications.
"""

import asyncio
import logging
from datetime import datetime
import threading
import time

from config import config
from session_manager import session_manager
from telegram_bot_handler import bot_handler

logger = logging.getLogger(__name__)


class SessionService:
    """Service for monitoring sessions and sending notifications."""
    
    def __init__(self):
        self._running = False
        self._monitor_thread = None
        self._notified_minutes = set()  # Track which notifications we've sent
    
    def start(self):
        """Start the session monitoring service."""
        if self._running:
            logger.warning("Session service is already running")
            return
        
        self._running = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("Session service started")
    
    def stop(self):
        """Stop the session monitoring service."""
        self._running = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Session service stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while self._running:
            try:
                self._check_session_expiry()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Error in session monitoring: {e}")
                time.sleep(60)
    
    def _check_session_expiry(self):
        """Check session expiry and send notifications."""
        session = session_manager.get_session()
        
        if not session:
            # Reset notification tracking when no session
            self._notified_minutes.clear()
            return
        
        minutes_left = session.minutes_until_expiry
        
        # Check if session has expired
        if session.is_expired:
            if 0 not in self._notified_minutes:
                asyncio.create_task(self._send_expiry_notification(0))
                self._notified_minutes.add(0)
            return
        
        # Check for upcoming expiry notifications
        for notify_minutes in config.NOTIFY_BEFORE_EXPIRY_MINUTES:
            if (minutes_left <= notify_minutes and 
                notify_minutes not in self._notified_minutes and
                minutes_left > 0):
                
                asyncio.create_task(self._send_expiry_notification(notify_minutes))
                self._notified_minutes.add(notify_minutes)
        
        # Reset notifications if session was refreshed
        if minutes_left > max(config.NOTIFY_BEFORE_EXPIRY_MINUTES):
            self._notified_minutes.clear()
    
    async def _send_expiry_notification(self, minutes_left: int):
        """Send expiry notification via Telegram."""
        try:
            if minutes_left == 0:
                message = (
                    "🚨 *Session Expired*\n\n"
                    "Your Brain API session has expired.\n"
                    "Use `/login` to authenticate again."
                )
            elif minutes_left == 1:
                message = (
                    "⚠️ *Session Expiring Soon*\n\n"
                    "Your session expires in 1 minute!\n"
                    "Use `/login` to refresh your session."
                )
            else:
                message = (
                    f"⚠️ *Session Expiring Soon*\n\n"
                    f"Your session expires in {minutes_left} minutes.\n"
                    "Use `/login` to refresh your session."
                )
            
            await bot_handler.send_notification(message)
            logger.info(f"Sent expiry notification for {minutes_left} minutes")
            
        except Exception as e:
            logger.error(f"Failed to send expiry notification: {e}")
    
    async def notify_custom(self, message: str):
        """Send custom notification via Telegram."""
        try:
            await bot_handler.send_notification(message)
            logger.info("Sent custom notification")
        except Exception as e:
            logger.error(f"Failed to send custom notification: {e}")


# Global session service instance
session_service = SessionService()
